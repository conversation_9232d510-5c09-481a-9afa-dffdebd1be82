# NLTK Text Humanizer Service Integration Guide

## 1. Overview

This guide details the integration of the NLTK-based text humanization service, which provides intelligent text processing capabilities for making AI-generated text appear more human-like. The service uses Natural Language Toolkit (NLTK) and TextBlob for advanced linguistic processing.

The NLTK humanizer runs as a separate Python Flask microservice. The main Next.js backend application communicates with this Python service via HTTP requests to get humanized text. This separation allows for dedicated management of the Python environment and the resource-intensive natural language processing operations.

## 2. Python NLTK Humanizer Service (`python_services/nltk_humanizer/app.py`)

This service exposes an API endpoint that accepts text and returns its humanized version using NLTK and TextBlob.

### Key Features:

- **Intelligent Synonym Replacement**: Uses WordNet to find contextually appropriate synonyms
- **Part-of-Speech Aware Processing**: Leverages TextBlob for accurate POS tagging
- **Symbol and Formatting Preservation**: Maintains original text structure and punctuation
- **Sentence-level Processing**: Processes text at the sentence level for better context awareness

### Core Functions:

#### `replace_word(word, pos)`
Replaces a word with its most common synonym based on part of speech:
- Uses WordNet synsets to find synonyms
- Filters out the original word
- Returns the most frequent synonym based on corpus frequency

#### `clean_symbols(text)`
Cleans up text formatting:
- Removes extra spaces
- Fixes unusual symbol placement
- Maintains proper punctuation spacing

#### `humanize_text(text)`
Main humanization function:
- Preserves newlines using placeholder technique
- Splits text into sentences while preserving symbols
- Applies POS tagging using TextBlob
- Replaces adjectives (JJ tags) and adverbs (RB tags) with synonyms
- Reconstructs text maintaining original structure

## 3. API Endpoints

### POST `/humanize`
Humanizes the provided text using NLTK processing.

**Request Body:**
```json
{
    "text": "Your AI-generated text here"
}
```

**Success Response:**
```json
{
    "humanized_text": "The humanized version of your text"
}
```

**Error Response:**
```json
{
    "error": "Error message describing what went wrong"
}
```

### GET `/health`
Health check endpoint for service monitoring.

**Response:**
```json
{
    "status": "healthy",
    "service": "NLTK Text Humanizer"
}
```

## 4. Installation and Setup

### Prerequisites
- Python 3.9 or higher
- pip package manager

### Local Development Setup

1. **Navigate to the service directory:**
   ```bash
   cd python_services/nltk_humanizer
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the service:**
   ```bash
   python app.py
   ```

The service will start on `http://localhost:5002` by default.

### Docker Setup

1. **Build the Docker image:**
   ```bash
   cd python_services/nltk_humanizer
   docker build -t nltk-humanizer-service .
   ```

2. **Run the container:**
   ```bash
   docker run -p 5002:5002 nltk-humanizer-service
   ```

### Environment Variables

- `FLASK_PORT`: Port number for the service (default: 5002)

## 5. Next.js Backend Integration (`src/services/nltkHumanizerService.js`)

The Next.js backend communicates with the Python Flask service using the `fetch` API. The logic for this communication is encapsulated in `src/services/nltkHumanizerService.js`.

### Key Functions:

#### `humanizeWithNLTK(textToHumanize)`
Main function to call the NLTK humanizer service:
- Validates input text
- Makes HTTP POST request to the service
- Handles errors gracefully
- Returns structured response object

#### `checkNLTKHumanizerHealth()`
Health check function for service monitoring:
- Calls the `/health` endpoint
- Returns service status information

### Configuration

The service URL can be configured via environment variable:
```bash
NEXT_PUBLIC_NLTK_HUMANIZER_API_URL=http://localhost:5002/humanize
```

## 6. Integration in Processing Pipeline

The NLTK humanizer is integrated into the main text processing pipeline in `src/pages/api/process.js`:

1. **Step 1**: PEGASUS paraphrasing (advanced AI-based paraphrasing)
2. **Step 2**: NLTK humanization (intelligent synonym replacement and linguistic processing)
3. **Step 3**: Basic text modifications (controlled mistakes, style changes)
4. **Step 4**: AI detection check

This multi-step approach provides comprehensive text humanization using different techniques.

## 7. Error Handling and Fallbacks

The service includes robust error handling:
- Network errors are caught and logged
- Service failures don't break the main processing pipeline
- Graceful degradation when the service is unavailable
- Detailed error messages for debugging

## 8. Performance Considerations

- **NLTK Data Download**: Required NLTK data is downloaded automatically on service startup
- **Memory Usage**: TextBlob and WordNet require moderate memory for linguistic data
- **Processing Time**: Sentence-by-sentence processing may take longer for very large texts
- **Caching**: Consider implementing caching for frequently processed text patterns

## 9. Monitoring and Maintenance

### Health Checks
Use the `/health` endpoint to monitor service availability:
```bash
curl http://localhost:5002/health
```

### Logs
The service logs important events and errors. Monitor logs for:
- NLTK data download status
- Processing errors
- Performance issues

### Updates
Keep NLTK and TextBlob updated for the latest linguistic improvements:
```bash
pip install --upgrade nltk textblob
```

## 10. Troubleshooting

### Common Issues:

1. **NLTK Data Not Found**: Ensure NLTK data is properly downloaded
2. **Port Conflicts**: Check if port 5002 is available
3. **Memory Issues**: Monitor memory usage for large text processing
4. **Slow Processing**: Consider text length limits for better performance

### Debug Mode:
Run the service with debug logging:
```bash
FLASK_DEBUG=1 python app.py
```
