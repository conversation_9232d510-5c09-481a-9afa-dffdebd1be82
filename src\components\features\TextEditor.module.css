.editorContainer {
    display: flex;
    flex-direction: column; /* Stack textareas vertically on small screens */
    gap: 20px; /* Space between textareas */
    margin-bottom: 20px;
}

.editorWrapper { /* If needed for individual textarea styling or future elements */
    width: 100%;
}

.textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 1rem;
    line-height: 1.5;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.06);
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    min-height: 180px; /* Ensure a decent default height */
    font-family: inherit; /* Inherit font from body */
}

.textarea:focus {
    border-color: #0070f3; /* Next.js blue */
    box-shadow: 0 0 0 3px rgba(0, 112, 243, 0.2);
    outline: none;
}

.textareaOutput {
    background-color: #e9ecef; /* Slightly different background for readonly output */
    color: #495057;
}

/* For larger screens, consider side-by-side layout if desired */
@media (min-width: 768px) {
    .editorContainer {
        flex-direction: row; /* Side-by-side on larger screens */
    }
    .editorWrapper {
        flex: 1; /* Each textarea takes half the width */
    }
}
