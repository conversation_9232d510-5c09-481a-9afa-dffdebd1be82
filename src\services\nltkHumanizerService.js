// src/services/nltkHumanizerService.js

// Default URL for the Python NLTK humanizer service
// This can be overridden by setting the NEXT_PUBLIC_NLTK_HUMANIZER_API_URL environment variable
const NLTK_HUMANIZER_API_URL = process.env.NEXT_PUBLIC_NLTK_HUMANIZER_API_URL || 'http://localhost:5002/humanize';

/**
 * Calls the external Python service to humanize text using NLTK and TextBlob.
 * @param {string} textToHumanize - The text string to be humanized.
 * @returns {Promise<object>} A promise that resolves to an object.
 * On success: { humanized_text: "...", error: false }
 * On failure: { error: true, message: "Error message details" }
 */
export async function humanizeWithNLTK(textToHumanize) {
    if (!textToHumanize || typeof textToHumanize !== 'string' || !textToHumanize.trim()) {
        return { error: true, message: 'Input text is required and must be a non-empty string.' };
    }

    try {
        const response = await fetch(NLTK_HUMANIZER_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: textToHumanize }),
        });

        if (!response.ok) {
            // Handle HTTP error responses
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
            
            try {
                const errorData = await response.json();
                if (errorData.error) {
                    errorMessage = errorData.error;
                }
            } catch (jsonError) {
                // If we can't parse the error response as JSON, use the HTTP status
                console.warn('Could not parse error response as JSON:', jsonError);
            }

            return { error: true, message: errorMessage };
        }

        const data = await response.json();

        if (data.humanized_text && typeof data.humanized_text === 'string') {
            return { humanized_text: data.humanized_text, error: false };
        } else {
            return { error: true, message: 'Invalid response format from NLTK humanizer service.' };
        }

    } catch (networkError) {
        console.error('Network error calling NLTK humanizer service:', networkError);
        return { 
            error: true, 
            message: `Network error: ${networkError.message}. Is the NLTK humanizer service running on ${NLTK_HUMANIZER_API_URL}?` 
        };
    }
}

/**
 * Health check for the NLTK humanizer service
 * @returns {Promise<object>} A promise that resolves to health status
 */
export async function checkNLTKHumanizerHealth() {
    const healthUrl = NLTK_HUMANIZER_API_URL.replace('/humanize', '/health');
    
    try {
        const response = await fetch(healthUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            return { healthy: false, message: `HTTP ${response.status}: ${response.statusText}` };
        }

        const data = await response.json();
        return { healthy: true, data };

    } catch (error) {
        return { healthy: false, message: `Network error: ${error.message}` };
    }
}
