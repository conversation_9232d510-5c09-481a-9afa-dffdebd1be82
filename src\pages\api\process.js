// Import other utilities and the GPTZero client
import { addControlledMistakes, changeStyle } from '../../utils/textModifiers'; // Removed simpleParaphrase for now
import { checkWithGPTZero } from '../../services/gptzeroClient';
import { paraphraseWithPegasus } from '../../services/paraphraseService'; // Import the new PEGASUS service client
import { humanizeWithNLTK } from '../../services/nltkHumanizerService'; // Import the NLTK humanizer service client

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { text } = req.body;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return res.status(400).json({ message: 'Input text is required and must be a non-empty string.' });
    }

    let modifiedText = text; // Start with the original text

    try {
        // --- Step 1: Advanced Paraphrasing with PEGASUS Microservice ---
        console.log("Calling PEGASUS paraphrasing service...");
        const pegasusResult = await paraphraseWithPegasus(modifiedText);

        if (pegasusResult && !pegasusResult.error && typeof pegasusResult.paraphrased_text === 'string') {
            modifiedText = pegasusResult.paraphrased_text;
            console.log("Successfully paraphrased with PEGASUS service.");
        } else {
            // Log the error/warning but proceed with the current version of modifiedText
            // This means if PEGASUS fails, we use the original text (or text after prior steps) for subsequent modifications.
            console.warn(`PEGASUS paraphrasing service call failed or returned an error: ${pegasusResult?.message}. Proceeding without PEGASUS paraphrasing.`);
            // Optional: Implement a fallback to a simpler, local paraphrasing method here if critical
            // For example: modifiedText = await simpleParaphraseLocalFallback(modifiedText);
            // For now, we just log and continue. The old `simpleParaphrase` which called Datamuse
            // is effectively replaced by this PEGASUS call.
        }

        // --- Step 2: NLTK-based Humanization ---
        console.log("Calling NLTK humanizer service...");
        const nltkResult = await humanizeWithNLTK(modifiedText);

        if (nltkResult && !nltkResult.error && typeof nltkResult.humanized_text === 'string') {
            modifiedText = nltkResult.humanized_text;
            console.log("Successfully humanized with NLTK service.");
        } else {
            console.warn(`NLTK humanizer service call failed or returned an error: ${nltkResult?.message}. Proceeding without NLTK humanization.`);
        }

        // --- Step 3: Apply other subtle modifications from textModifiers.js ---
        // These functions from textModifiers.js are synchronous and operate on the text
        console.log("Applying controlled mistakes...");
        modifiedText = addControlledMistakes(modifiedText);

        console.log("Applying style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 4: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        res.status(200).json({ modifiedText, detectionResult });

    } catch (error) {
        // This catches errors from textModifiers or other unexpected issues within this handler
        console.error("Error in /api/process main try block:", error);
        const errorMessage = error.message || 'Error processing text.';
        res.status(500).json({
            message: errorMessage,
            error: error.toString(),
            detectionResult: { // Ensure detectionResult has a consistent error structure
                error: true,
                status: "Server Error",
                message: "Failed to process text due to an internal server error in the API handler.",
                score: null
            }
        });
    }
}
