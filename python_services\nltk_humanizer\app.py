from flask import Flask, request, jsonify
import nltk
from textblob import TextBlob
import re
from nltk.corpus import wordnet
import os

# Initialize Flask App
app = Flask(__name__)

# Download required NLTK data
def download_nltk_data():
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
        nltk.download('wordnet', quiet=True)
        print("NLTK data downloaded successfully.")
    except Exception as e:
        print(f"Error downloading NLTK data: {e}")

# Call download function when the application starts
download_nltk_data()

def replace_word(word, pos):
    """
    Replace a word with its most common synonym based on part of speech
    """
    synonyms = []
    for syn in wordnet.synsets(word, pos=pos):
        for lemma in syn.lemmas():
            if lemma.name().lower() != word.lower():
                synonyms.append(lemma.name())
    
    if synonyms:
        # Choose the most common synonym based on its frequency in the corpus
        freq_dist = nltk.FreqDist(synonyms)
        most_common_synonym = freq_dist.max()
        return most_common_synonym
    else:
        return word

def clean_symbols(humanized_text):
    """
    Remove extra spaces and fix unusual symbol placement
    """
    # Remove extra spaces and unusual symbol placement
    humanized_text = re.sub(r'\s+', ' ', humanized_text)
    humanized_text = re.sub(r'\s([^\w\s])', r'\1', humanized_text)
    humanized_text = re.sub(r'([^\w\s])\s', r'\1', humanized_text)
    return humanized_text

def humanize_text(text):
    """
    Main function to humanize AI-generated text using NLTK and TextBlob
    """
    if not text or not isinstance(text, str):
        return text
    
    # Use a unique placeholder for newlines to preserve them
    newline_placeholder = "庄周"
    text = text.replace('\n', newline_placeholder)

    # Split text into sentences while preserving symbols
    sentences = re.findall(r'[^.!?]+[.!?]+|[^\w\s]+', text)

    # Iterate over each sentence and humanize it
    humanized_sentences = []
    for sentence in sentences:
        # Split sentence into words while preserving symbols
        words = re.findall(r'\w+|[^\w\s]+', sentence)

        # Use TextBlob to get the part-of-speech tags for each word in the sentence
        try:
            tags = TextBlob(sentence).tags
        except Exception as e:
            print(f"Error processing sentence: {e}")
            continue

        # Replace adjectives and adverbs with more human-like alternatives
        humanized_words = []
        for word, tag in tags:
            if tag.startswith('JJ'):  # Adjectives
                humanized_word = replace_word(word, 'a')
                humanized_words.append(humanized_word)
            elif tag.startswith('RB'):  # Adverbs
                humanized_word = replace_word(word, 'r')
                humanized_words.append(humanized_word)
            else:
                humanized_words.append(word)

        # Join the humanized words back into a sentence with symbols
        humanized_sentence = ''
        for i in range(len(words)):
            if words[i].isalpha() and i < len(humanized_words):
                humanized_sentence += humanized_words[i] + ' '
            else:
                humanized_sentence += words[i]

        humanized_sentences.append(humanized_sentence)

    # Join the humanized sentences back into a single text with symbols
    humanized_text = ''.join(humanized_sentences)

    # Clean up symbols and restore newlines
    humanized_text = clean_symbols(humanized_text)
    humanized_text = humanized_text.replace(newline_placeholder, '\n')

    return humanized_text.strip()

# --- API Endpoint ---
@app.route('/humanize', methods=['POST'])
def humanize_text_endpoint():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        input_text = data.get('text')

        if not input_text:
            return jsonify({"error": "No text provided in JSON payload (expected key 'text')"}), 400

        if not isinstance(input_text, str):
            return jsonify({"error": "Field 'text' must be a string"}), 400

        # Process the text using NLTK humanization
        humanized_result = humanize_text(input_text)

        return jsonify({"humanized_text": humanized_result})

    except Exception as e:
        app.logger.error(f"Error during text humanization: {e}", exc_info=True)
        return jsonify({"error": "An unexpected error occurred during text humanization."}), 500

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({"status": "healthy", "service": "NLTK Text Humanizer"})

# --- Run Flask App ---
if __name__ == '__main__':
    # Port can be configured via an environment variable or default to 5002
    port = int(os.environ.get('FLASK_PORT', 5002))
    # Note: debug=True is for development. Set to False in production.
    app.run(host='0.0.0.0', port=port, debug=True)
