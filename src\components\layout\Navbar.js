import React from 'react';
import Link from 'next/link';
import styles from './Navbar.module.css';
import AuthButtons from '../auth/AuthButtons'; // Import AuthButtons

const Navbar = () => {
    return (
        <nav className={styles.navbar}>
            <div className={styles.container}>
                <Link href="/" legacyBehavior>
                    <a className={styles.title}>StealthWriter AI</a>
                </Link>

                {/* This div is a placeholder for actual nav links if added later.
                    If no other links, AuthButtons will be pushed to the right by justify-content: space-between.
                    If there are links, this div will group them.
                */}
                <div className={styles.navLinks}>
                    {/* Example: <Link href="/features"><a>Features</a></Link> */}
                    {/* Example: <Link href="/pricing"><a>Pricing</a></Link> */}
                </div>

                {/* Authentication buttons will typically be on the far right */}
                <AuthButtons />
            </div>
        </nav>
    );
};

export default Navbar;
