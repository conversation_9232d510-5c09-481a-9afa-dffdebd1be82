import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import TextEditor from '../components/features/TextEditor';
import ControlPanel from '../components/features/ControlPanel';
import ResultsDisplay from '../components/features/ResultsDisplay';
import Layout from '../components/layout/Layout';
import AdBanner from '../components/ads/AdBanner'; // Import AdBanner
import { processTextApi } from '../utils/apiClient';
import styles from '../styles/Home.module.css';

export default function HomePage() {
    const { data: session, status: sessionStatus } = useSession();
    const isSessionLoading = sessionStatus === 'loading';

    const [inputText, setInputText] = useState('');
    const [outputText, setOutputText] = useState('');
    const [detectionResult, setDetectionResult] = useState(null);
    const [isProcessing, setIsProcessing] = useState(false); // For text processing API call
    const [processingError, setProcessingError] = useState('');

    const handleProcessText = async () => {
        if (!inputText.trim()) {
            setProcessingError('Input text cannot be empty.');
            return;
        }
        setIsProcessing(true);
        setProcessingError('');
        setOutputText('');
        setDetectionResult(null);
        try {
            const response = await processTextApi({ text: inputText });
            setOutputText(response.modifiedText);
            setDetectionResult(response.detectionResult);
        } catch (err) {
            setProcessingError(err.message || 'Failed to process text.');
            console.error('Processing error:', err);
        } finally {
            setIsProcessing(false);
        }
    };

    // Determine if ads should be shown
    // Show ads if:
    // 1. Session is loaded (not loading)
    // 2. User is not authenticated (session is null) OR user is authenticated and on the 'free' tier.
    const showAds = !isSessionLoading && (!session || session.user.subscriptionTier === 'free');

    return (
        <Layout>
            <div className={styles.container}>
                <h1 className={styles.title}>AI Text Humanizer</h1>
                <p className={styles.description}>
                    Modify your AI-generated text to sound more human.
                </p>

                {isSessionLoading && <p className={styles.loadingMessage}>Loading session...</p>}

                {!session && !isSessionLoading && (
                    <p className={styles.signInPrompt}>
                        Please <Link href="/api/auth/signin/google" legacyBehavior><a onClick={(e) => { e.preventDefault(); window.location.href = '/api/auth/signin/google';}}>sign in</a></Link> to save your work and access your full benefits. Guests have limited access.
                    </p>
                )}

                {session && !isSessionLoading && ( // Only show status box if session is loaded
                    <div className={styles.statusBox}>
                        <p>Welcome, <strong>{session.user.name || session.user.email}</strong>!</p>
                        <p>
                            Your current status: <span className={session.user.subscriptionTier === 'premium' ? styles.premiumStatus : styles.freeStatus}>
                                {session.user.subscriptionTier ? session.user.subscriptionTier.charAt(0).toUpperCase() + session.user.subscriptionTier.slice(1) : 'Free'} User
                            </span>
                        </p>
                        {session.user.subscriptionTier === 'free' && (
                            <p className={styles.upgradePrompt}>
                                <Link href="/pricing" legacyBehavior><a>Unlock more features! Upgrade to Premium.</a></Link>
                            </p>
                        )}
                    </div>
                )}

                {processingError && <p className={styles.error}>{processingError}</p>}

                <ControlPanel
                    onProcessText={handleProcessText}
                    isLoading={isProcessing}
                    disabled={!inputText.trim() || isProcessing}
                />

                <TextEditor
                    inputText={inputText}
                    onInputChange={(e) => setInputText(e.target.value)}
                    outputText={outputText}
                    isLoading={isProcessing}
                />

                {detectionResult && !isProcessing && (
                    <ResultsDisplay result={detectionResult} />
                )}

                {/* Conditionally render AdBanner */}
                {showAds && (
                    <div className={styles.adContainer}>
                        <AdBanner
                            // Replace with your actual AdSense Publisher ID and Ad Slot ID
                            // These are placeholders and will show the development placeholder from AdBanner.js
                            dataAdClient="ca-pub-YOUR_PUBLISHER_ID"
                            dataAdSlot="YOUR_AD_SLOT_ID"
                            // You can also use adHtmlBlock for other ad providers:
                            // adHtmlBlock={`<div><p>Your Generic Ad HTML/JS Snippet Here</p></div>`}
                        />
                    </div>
                )}
            </div>
        </Layout>
    );
}
