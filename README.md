# Project Title Suggestion: StealthWriter AI

*(Other suggestions: UndetectAI, HumanizerAI, GhostWrite, VeriMod)*

## General Concept

StealthWriter AI is a web application designed to help users modify AI-generated text. The goal is to make the text less detectable by common AI detection tools like GPTZero, Turnitin (conceptual), and others, by rephrasing content and introducing human-like stylistic variations.

## Key Features (MVP)

*   **Text Input:** Users can paste or upload AI-generated text.
*   **Text Processing Engine:**
    *   **Advanced Paraphrasing:** Uses the PEGASUS model for sophisticated text paraphrasing while preserving meaning.
    *   **NLTK-based Humanization:** Employs Natural Language Toolkit (NLTK) with TextBlob for intelligent synonym replacement, POS tagging, and human-like text variations.
    *   **Controlled Grammar Variations:** Intentionally adds slight, natural-sounding variations that can mimic human writing patterns.
    *   **Style Optimization:** Modifies sentence structure and vocabulary to sound more human-like.
    *   **Human-like Anomalies:** Introduces subtle elements like minor redundancies or varied phrasing.
*   **AI Detection Test (Conceptual):**
    *   Integrates with available (preferably free-tier) AI detection APIs (e.g., GPTZero) to provide feedback on how the modified text might be classified.
*   **Output & Export:** Users can easily copy the improved text. Downloading as a `.txt` file can be a future addition.

## Technology Stack

*   **Frontend:** React, Next.js
*   **Backend:** Node.js (via Next.js API Routes for MVP)
*   **Text Processing Services:**
    *   Python Flask microservice with PEGASUS model for advanced paraphrasing
    *   Python Flask microservice with NLTK/TextBlob for intelligent text humanization
*   **Styling:** CSS Modules (or chosen CSS framework like Tailwind CSS - TBD)
*   **Deployment:** Vercel, Netlify, or any Node.js compatible hosting.

## System Architecture

The application uses a hybrid architecture. The frontend is built with Next.js, and the backend logic for text processing and AI detection API calls is handled by Next.js API routes.

For a detailed explanation of the system architecture, please see:
[System Architecture Document](./docs/system_architecture.md)

## Workflow

The user inputs text, which is sent to the backend. The backend processes the text using a series of modification algorithms and optionally checks it against an AI detection service. The modified text and detection results are then returned to the user.

For a visual representation and detailed steps of the workflow, please see:
[Workflow Document](./docs/workflow.md)

*(Alternatively, embed Mermaid diagram here if preferred for simplicity in main README)*
```mermaid
sequenceDiagram
    participant User
    participant Frontend as Frontend (Next.js)
    participant Backend as Backend API (Next.js API Route)
    participant PEGASUS as PEGASUS Paraphrasing Service
    participant NLTK as NLTK Humanizer Service
    participant TextMod as Text Modifiers (Own Algorithms)
    participant AIDetect as External AI Detection API (e.g., GPTZero)
    
    User->>Frontend: 1. Pastes or uploads text
    User->>Frontend: 2. Clicks "Process Text" button
    Frontend->>Backend: 3. Sends text to /api/process
    activate Backend
    Backend->>PEGASUS: 4. Sends text for paraphrasing
    activate PEGASUS
    PEGASUS-->>Backend: 5. Returns paraphrased text
    deactivate PEGASUS
    Backend->>NLTK: 6. Sends text for humanization
    activate NLTK
    NLTK-->>Backend: 7. Returns humanized text
    deactivate NLTK
    Backend->>TextMod: 8. Applies additional text modifications
    activate TextMod
    TextMod-->>Backend: 9. Returns modified text
    deactivate TextMod
    alt Optional: AI Detection Check
        Backend->>AIDetect: 10. Sends modified text for detection
        activate AIDetect
        AIDetect-->>Backend: 11. Returns detection score/status
        deactivate AIDetect
        Backend->>Backend: 12. Combines modified text & detection results
    end
    Backend-->>Frontend: 13. Sends processed text (and detection results)
    deactivate Backend
    Frontend->>User: 14. Displays modified text and AI detection score
    User->>Frontend: 15. Copies or downloads modified text
```

## Project Structure

The project follows a standard Next.js structure with a `src` directory. Key areas include `src/pages` for routes and API handlers, `src/components` for React components, and `src/utils` for core logic like text modifiers.

For a detailed view of the project structure, please see:
[Project Structure Document](./docs/project_structure.md)

## Getting Started

These are basic instructions to get the Next.js development server running.

**Prerequisites:**
*   Node.js (v18.x or later recommended)
*   npm or yarn

**Installation & Setup:**

1.  **Clone the repository (once it's on GitHub):**
    ```bash
    git clone <repository-url>
    cd stealthwriter-ai
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Set up Environment Variables:**

    Create a file named `.env.local` in the root of your project. This file will store all required environment variables. **Important:** Add `.env.local` to your `.gitignore` file to prevent committing sensitive credentials.

    Here's an example structure for your `.env.local` file:

    ```plaintext
    # .env.local - Keep this file secure and out of version control!

    # Google OAuth Credentials for NextAuth.js
    # Obtain these from the Google Cloud Console by setting up an OAuth 2.0 client ID.
    # Ensure the authorized JavaScript origins and redirect URIs are correctly configured.
    # For development, a common redirect URI is http://localhost:3000/api/auth/callback/google
    GOOGLE_CLIENT_ID=your_google_client_id_here
    GOOGLE_CLIENT_SECRET=your_google_client_secret_here

    # NextAuth.js Configuration
    # A strong, random string used to encrypt JWTs and sign cookies.
    # Generate one using: `openssl rand -base64 32` or from https://generate-secret.vercel.app/32
    NEXTAUTH_SECRET=your_super_strong_random_nextauth_secret
    # The canonical URL of your application.
    # For development:
    NEXTAUTH_URL=http://localhost:3000
    # For production (replace with your actual domain):
    # NEXTAUTH_URL=https://yourdomain.com

    # Database URL
    # Defines the connection string for your database.
    # For local development with SQLite (default after `npx prisma init --datasource-provider sqlite`):
    DATABASE_URL="file:./prisma/dev.db"
    # For PostgreSQL, it would look like:
    # DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE?schema=public"
    # See docs/database_setup.md for more details.

    # External API Services Configuration
    # URL for the Python PEGASUS paraphrasing microservice (if you are running it)
    # Ensure this matches the host and port where your Python service is running.
    NEXT_PUBLIC_PARAPHRASE_API_URL=http://localhost:5001/paraphrase

    # URL for the Python NLTK humanizer microservice (if you are running it)
    # Ensure this matches the host and port where your Python service is running.
    NEXT_PUBLIC_NLTK_HUMANIZER_API_URL=http://localhost:5002/humanize

    # GPTZero API Key (for AI detection feature)
    # Obtain this from your GPTZero dashboard if you are using their API.
    GPTZERO_API_KEY=your_gptzero_api_key_here

    # Python Flask Service Port (Optional)
    # This is for the Python paraphrasing service (python_services/paraphrase_pegasus/app.py).
    # The Python app defaults to 5001 if this is not set. Only set this if you need to change it.
    # FLASK_PORT=5001

    # --- Stripe Configuration (for Premium Subscriptions) ---
    # Obtain these from your Stripe Dashboard.
    # Important: Use test keys for development and live keys for production.
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key # Client-side key
    STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key                     # Server-side key, KEEP SECRET!
    STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret               # For verifying webhooks from Stripe
    PREMIUM_PLAN_PRICE_ID=price_your_premium_plan_id                     # Example Stripe Price ID for your main premium plan
    ```
    For detailed database setup, see the [Database Setup Guide](./docs/database_setup.md).
    For how subscriptions are handled, see the [Subscription System Design](./docs/subscription_system_design.md).
    For a list of premium features, see [Premium Features](./docs/premium_features.md).

    **Notes on Obtaining Credentials:**
    *   **Google Credentials:** Visit the [Google Cloud Console](https://console.cloud.google.com/). Create a new project or select an existing one. Navigate to "APIs & Services" > "Credentials". Click "Create Credentials" > "OAuth client ID". Choose "Web application" as the type. Add `http://localhost:3000` to "Authorized JavaScript origins" and `http://localhost:3000/api/auth/callback/google` to "Authorized redirect URIs" for development. For production, use your production URLs.
    *   **`NEXTAUTH_SECRET`:** This is critical for security. Use a long, random, and unique string. The command `openssl rand -base64 32` is a good way to generate one in your terminal.

4.  **Set up and run Python services (optional but recommended):**

    The application includes two Python microservices for enhanced text processing:

    **PEGASUS Paraphrasing Service:**
    ```bash
    cd python_services/paraphrase_pegasus
    pip install -r requirements.txt
    python app.py
    ```
    This will start the PEGASUS service on port 5001.

    **NLTK Text Humanizer Service:**
    ```bash
    cd python_services/nltk_humanizer
    pip install -r requirements.txt
    python app.py
    ```
    This will start the NLTK humanizer service on port 5002.

    **Quick Start (using provided scripts):**
    ```bash
    # Start both services at once
    ./scripts/start-python-services.sh

    # Test both services to ensure they're working
    python scripts/test-python-services.py

    # Stop both services
    ./scripts/stop-python-services.sh
    ```

    **Using Docker (alternative):**
    ```bash
    # For PEGASUS service
    cd python_services/paraphrase_pegasus
    docker build -t pegasus-service .
    docker run -p 5001:5001 pegasus-service

    # For NLTK humanizer service
    cd python_services/nltk_humanizer
    docker build -t nltk-humanizer-service .
    docker run -p 5002:5002 nltk-humanizer-service
    ```

5.  **Run the development server:**
    ```bash
    npm run dev
    # or
    yarn dev
    ```
    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Usage

### Text Humanization Process

The application uses a multi-step approach to humanize AI-generated text:

1. **PEGASUS Paraphrasing**: Advanced AI-based paraphrasing using the `tuner007/pegasus_paraphrase` model
2. **NLTK Humanization**: Intelligent text processing using Natural Language Toolkit:
   - **Part-of-Speech Analysis**: Uses TextBlob to identify adjectives and adverbs
   - **Intelligent Synonym Replacement**: Leverages WordNet to find contextually appropriate synonyms
   - **Frequency-Based Selection**: Chooses the most common synonyms for natural language flow
   - **Structure Preservation**: Maintains original formatting, punctuation, and newlines
3. **Style Modifications**: Applies subtle changes like contractions and varied sentence starters
4. **Controlled Mistakes**: Adds minor human-like imperfections
5. **AI Detection Check**: Tests the final result against detection algorithms

### Python-Based Humanization Features

The NLTK humanizer service provides sophisticated linguistic processing:

- **Context-Aware Processing**: Analyzes text at the sentence level for better context understanding
- **POS-Targeted Replacement**: Specifically targets adjectives and adverbs for synonym replacement
- **Corpus-Based Selection**: Uses frequency distribution to select the most natural synonyms
- **Symbol Preservation**: Maintains all punctuation, spacing, and formatting from the original text
- **Newline Handling**: Uses placeholder technique to preserve paragraph structure

### Example Transformation

**Input (AI-generated):**
```
The innovative solution provides exceptional performance and remarkable efficiency.
```

**After NLTK Humanization:**
```
The creative solution provides outstanding performance and notable efficiency.
```

The system intelligently replaces:
- "innovative" → "creative" (adjective replacement)
- "exceptional" → "outstanding" (adjective replacement)
- "remarkable" → "notable" (adjective replacement)

While preserving the original sentence structure and meaning.

## Project Documentation

This project includes several key documents to help understand its architecture, setup, and features:

*   **Core Architecture & Design:**
    *   [System Architecture](./docs/system_architecture.md): Overall application architecture.
    *   [Project Structure](./docs/project_structure.md): Folder and file organization.
    *   [Workflow Diagram](./docs/workflow.md): User and data flow.
    *   [User Accounts & Authentication](./docs/user_accounts_architecture.md): Design for user accounts and auth.
*   **Feature-Specific Documentation:**
    *   [PEGASUS Integration Guide](./docs/pegasus_integration_guide.md): Setup and use of the Python paraphrasing service.
    *   [NLTK Humanizer Integration Guide](./docs/nltk_humanizer_integration_guide.md): Setup and use of the Python NLTK-based text humanization service.
    *   [Database Setup Guide](./docs/database_setup.md): Prisma ORM, schema, and migrations.
    *   [Premium Features](./docs/premium_features.md): Freemium vs. Premium feature list.
    *   [Subscription System Design](./docs/subscription_system_design.md): Stripe integration and subscription logic.
    *   [Ad Integration Strategy](./docs/ad_integration_strategy.md): Approach for incorporating ads.
*   **Development & Research:**
    *   [Open Source Text Models](./docs/open_source_text_models.md): Research on relevant OS models.
    *   [Example Commit Messages](./docs/example_commit_messages.md): Suggestions for consistent commit messages.

## Disclaimer

*   **MVP Nature:** This project is currently an MVP (Minimum Viable Product). The "own algorithms" for text modification are simplified and may not always produce text that reliably bypasses advanced AI detection tools.
*   **AI Detection:** The effectiveness of AI detection tools varies and is constantly evolving. Results from integrated services are indicative and not a guarantee. Free tier APIs often have strict rate limits or limited capabilities.
*   **Ethical Use:** This tool should be used responsibly and ethically. The intent is to help users understand how AI text can be modified and to explore text transformation techniques, not to encourage academic dishonesty or misrepresentation.
