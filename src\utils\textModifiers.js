// Basic text modification utilities
// These are simple, client-side modifications that complement the PEGASUS service

/**
 * Adds subtle, controlled mistakes that mimic human writing patterns
 * @param {string} text - The input text to modify
 * @returns {string} - Text with controlled mistakes added
 */
export function addControlledMistakes(text) {
    if (!text || typeof text !== 'string') return text;
    
    let modifiedText = text;
    
    // Add occasional double spaces (very subtle)
    modifiedText = modifiedText.replace(/\. /g, (match) => {
        return Math.random() < 0.1 ? '.  ' : match;
    });
    
    // Occasionally replace "and" with "&" in informal contexts
    modifiedText = modifiedText.replace(/\band\b/g, (match) => {
        return Math.random() < 0.05 ? '&' : match;
    });
    
    return modifiedText;
}

/**
 * Changes writing style to be more human-like
 * @param {string} text - The input text to modify
 * @returns {string} - Text with style changes applied
 */
export function changeStyle(text) {
    if (!text || typeof text !== 'string') return text;
    
    let modifiedText = text;
    
    // Add occasional contractions
    const contractions = {
        'do not': "don't",
        'will not': "won't",
        'cannot': "can't",
        'should not': "shouldn't",
        'would not': "wouldn't",
        'could not': "couldn't"
    };
    
    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        modifiedText = modifiedText.replace(regex, (match) => {
            return Math.random() < 0.3 ? contracted : match;
        });
    });
    
    // Vary sentence starters occasionally
    modifiedText = modifiedText.replace(/\bHowever,/g, (match) => {
        const alternatives = ['But,', 'Though,', 'Yet,', 'Still,'];
        return Math.random() < 0.4 ? alternatives[Math.floor(Math.random() * alternatives.length)] : match;
    });
    
    return modifiedText;
}
