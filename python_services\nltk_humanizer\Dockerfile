# Use the official Python runtime as a parent image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV FLASK_PORT=5002

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install Python packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code
COPY app.py .

# Create a non-root user to run the application
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser:appuser /app
USER appuser

# Expose the port the app runs on
EXPOSE ${FLASK_PORT}

# Command to run the application using Gunicorn for production
CMD ["gunicorn", "--bind", "0.0.0.0:5002", "--workers", "1", "--threads", "2", "--timeout", "120", "app:app"]

# Alternative for Flask development server (NOT FOR PRODUCTION):
# CMD ["python", "app.py"]
