import React from 'react';
import styles from './TextEditor.module.css'; // Import the CSS module

const TextEditor = ({ inputText, onInputChange, outputText, isLoading }) => {
    return (
        <div className={styles.editorContainer}>
            <div className={styles.editorWrapper}>
                <textarea
                    className={styles.textarea} // Apply base textarea style
                    value={inputText}
                    onChange={onInputChange}
                    placeholder="Paste your AI-generated text here..."
                    rows={15} // rows can still be a fallback or guide for min-height
                    disabled={isLoading}
                />
            </div>
            <div className={styles.editorWrapper}>
                <textarea
                    className={`${styles.textarea} ${styles.textareaOutput}`} // Apply base and output-specific styles
                    value={outputText}
                    placeholder="Humanized text will appear here..."
                    rows={15}
                    readOnly
                    disabled={isLoading} // Also disable output textarea when loading
                />
            </div>
        </div>
    );
};

export default TextEditor;
