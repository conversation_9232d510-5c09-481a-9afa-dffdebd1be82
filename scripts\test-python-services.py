#!/usr/bin/env python3
"""
Test script for StealthWriter AI Python microservices
This script tests both the PEGASUS paraphrasing service and NLTK humanizer service
"""

import requests
import json
import sys
import time

# Service configurations
PEGASUS_URL = "http://localhost:5001"
NLTK_HUMANIZER_URL = "http://localhost:5002"

# Test text
TEST_TEXT = "The innovative solution provides exceptional performance and remarkable efficiency for modern applications."

def test_service_health(service_name, health_url):
    """Test if a service is healthy and responding"""
    print(f"\n=== Testing {service_name} Health ===")
    try:
        response = requests.get(health_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ {service_name} is healthy")
            print(f"  Status: {data.get('status', 'unknown')}")
            print(f"  Service: {data.get('service', 'unknown')}")
            return True
        else:
            print(f"✗ {service_name} health check failed with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ {service_name} health check failed: {e}")
        return False

def test_pegasus_service():
    """Test the PEGASUS paraphrasing service"""
    print(f"\n=== Testing PEGASUS Paraphrasing Service ===")
    print(f"Input text: {TEST_TEXT}")
    
    try:
        response = requests.post(
            f"{PEGASUS_URL}/paraphrase",
            json={"text": TEST_TEXT},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            paraphrased_text = data.get("paraphrased_text", "")
            print(f"✓ PEGASUS paraphrasing successful")
            print(f"  Output: {paraphrased_text}")
            return paraphrased_text
        else:
            print(f"✗ PEGASUS paraphrasing failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"  Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ PEGASUS paraphrasing failed: {e}")
        return None

def test_nltk_humanizer_service():
    """Test the NLTK humanizer service"""
    print(f"\n=== Testing NLTK Humanizer Service ===")
    print(f"Input text: {TEST_TEXT}")
    
    try:
        response = requests.post(
            f"{NLTK_HUMANIZER_URL}/humanize",
            json={"text": TEST_TEXT},
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            humanized_text = data.get("humanized_text", "")
            print(f"✓ NLTK humanization successful")
            print(f"  Output: {humanized_text}")
            return humanized_text
        else:
            print(f"✗ NLTK humanization failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"  Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"  Response: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"✗ NLTK humanization failed: {e}")
        return None

def test_full_pipeline():
    """Test the full processing pipeline"""
    print(f"\n=== Testing Full Processing Pipeline ===")
    print(f"Original text: {TEST_TEXT}")
    
    # Step 1: PEGASUS paraphrasing
    paraphrased = test_pegasus_service()
    if not paraphrased:
        print("Pipeline test failed at PEGASUS step")
        return False
    
    # Step 2: NLTK humanization
    humanized = test_nltk_humanizer_service()
    if not humanized:
        print("Pipeline test failed at NLTK step")
        return False
    
    print(f"\n=== Pipeline Results ===")
    print(f"Original:    {TEST_TEXT}")
    print(f"Paraphrased: {paraphrased}")
    print(f"Humanized:   {humanized}")
    
    return True

def main():
    """Main test function"""
    print("StealthWriter AI Python Services Test")
    print("=" * 50)
    
    # Test service health
    pegasus_healthy = test_service_health("PEGASUS", f"{PEGASUS_URL}/health")
    nltk_healthy = test_service_health("NLTK Humanizer", f"{NLTK_HUMANIZER_URL}/health")
    
    if not pegasus_healthy:
        print(f"\n✗ PEGASUS service is not available at {PEGASUS_URL}")
        print("Make sure the service is running with: cd python_services/paraphrase_pegasus && python app.py")
    
    if not nltk_healthy:
        print(f"\n✗ NLTK Humanizer service is not available at {NLTK_HUMANIZER_URL}")
        print("Make sure the service is running with: cd python_services/nltk_humanizer && python app.py")
    
    if not (pegasus_healthy and nltk_healthy):
        print("\nSome services are not available. Exiting.")
        sys.exit(1)
    
    # Test individual services
    success = True
    
    if pegasus_healthy:
        result = test_pegasus_service()
        if not result:
            success = False
    
    if nltk_healthy:
        result = test_nltk_humanizer_service()
        if not result:
            success = False
    
    # Test full pipeline if both services are working
    if pegasus_healthy and nltk_healthy:
        pipeline_success = test_full_pipeline()
        if not pipeline_success:
            success = False
    
    print(f"\n{'=' * 50}")
    if success:
        print("✓ All tests passed! Services are working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
