#!/bin/bash

# <PERSON>ript to start both Python microservices for the StealthWriter AI application
# This script starts the PEGASUS paraphrasing service and NLTK humanizer service

echo "Starting StealthWriter AI Python Services..."

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "Port $port is already in use. Please stop the service using that port or change the port configuration."
        return 1
    fi
    return 0
}

# Function to start a service in the background
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    
    echo "Starting $service_name on port $port..."
    
    # Check if port is available
    if ! check_port $port; then
        return 1
    fi
    
    # Navigate to service directory
    cd "$service_dir" || {
        echo "Error: Cannot find directory $service_dir"
        return 1
    }
    
    # Check if requirements.txt exists
    if [ ! -f "requirements.txt" ]; then
        echo "Error: requirements.txt not found in $service_dir"
        return 1
    fi
    
    # Install dependencies if needed
    echo "Installing dependencies for $service_name..."
    pip install -r requirements.txt
    
    # Start the service in the background
    python app.py &
    local pid=$!
    
    echo "$service_name started with PID $pid"
    echo $pid > "${service_name,,}.pid"
    
    # Wait a moment and check if the service is still running
    sleep 2
    if ! kill -0 $pid 2>/dev/null; then
        echo "Error: $service_name failed to start"
        return 1
    fi
    
    echo "$service_name is running successfully on port $port"
    cd - > /dev/null
    return 0
}

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Start PEGASUS Paraphrasing Service
echo "=== Starting PEGASUS Paraphrasing Service ==="
if start_service "PEGASUS" "$PROJECT_ROOT/python_services/paraphrase_pegasus" 5001; then
    echo "✓ PEGASUS service started successfully"
else
    echo "✗ Failed to start PEGASUS service"
    exit 1
fi

echo ""

# Start NLTK Humanizer Service
echo "=== Starting NLTK Humanizer Service ==="
if start_service "NLTK-Humanizer" "$PROJECT_ROOT/python_services/nltk_humanizer" 5002; then
    echo "✓ NLTK Humanizer service started successfully"
else
    echo "✗ Failed to start NLTK Humanizer service"
    # Kill PEGASUS service if NLTK fails
    if [ -f "pegasus.pid" ]; then
        kill $(cat pegasus.pid) 2>/dev/null
        rm pegasus.pid
    fi
    exit 1
fi

echo ""
echo "=== All Services Started Successfully ==="
echo "PEGASUS Paraphrasing Service: http://localhost:5001"
echo "NLTK Humanizer Service: http://localhost:5002"
echo ""
echo "To stop the services, run: ./scripts/stop-python-services.sh"
echo "Or manually kill the processes using the PID files created in the service directories."
