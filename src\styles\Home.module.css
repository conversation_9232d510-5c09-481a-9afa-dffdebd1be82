.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem; /* Ensure consistent padding with Navbar/Footer containers */
    min-height: calc(70vh - 4rem); /* Adjust based on Layout.js main padding and potential header/footer heights */
}

.title {
    text-align: center;
    margin-bottom: 1rem; /* Increased margin */
    font-size: 2.8rem; /* Slightly larger title */
    font-weight: 600; /* Slightly bolder */
    color: #2c3e50; /* Match Navbar dark color for consistency */
}

.description {
    text-align: center;
    margin-bottom: 2.5rem; /* Increased margin */
    font-size: 1.15rem; /* Slightly larger */
    color: #555e68; /* Softer color */
    line-height: 1.7;
}

.error {
    color: #e74c3c; /* More distinct error color */
    background-color: #fddfe2;
    border: 1px solid #f5c6cb;
    padding: 15px;
    margin-bottom: 1.5rem;
    text-align: center;
    border-radius: 6px;
    font-weight: 500;
}

.loadingMessage {
    font-style: italic;
    color: #555e68; /* Match description color */
    text-align: center;
    padding: 20px 0;
    font-size: 1.1rem;
}

/* Optional: Add some overall spacing for the main sections if needed */
.mainContentArea {
    margin-top: 20px;
}

/* Styles for authentication-related conditional UI */
.protectedContent { /* Used on client-protected.js and potentially index.js for signed-in specific content */
    background-color: #e8f4fd; /* Light blue background */
    border: 1px solid #bde3fc; /* Blue border */
    color: #2a5296; /* Darker blue text */
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    text-align: center;
}

.protectedContent h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.signInPrompt {
    background-color: #fff3cd; /* Light yellow background */
    border: 1px solid #ffeeba; /* Yellow border */
    color: #856404; /* Darker yellow text */
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
}

.signInPrompt a {
    color: #0056b3; /* Standard link blue, or match your theme */
    font-weight: bold;
    text-decoration: underline;
}

.signInPrompt a:hover {
    color: #003d80;
}

/* New styles for status box on index.js */
.statusBox {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 25px;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
}

.statusBox p {
    margin: 0.5rem 0;
}

.premiumStatus {
    color: #28a745; /* Green */
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: #e6ffe6;
}

.freeStatus {
    color: #fd7e14; /* Orange */
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: #fff8e1;
}

.upgradePrompt {
    margin-top: 0.75rem !important;
    font-size: 0.95rem;
}

.upgradePrompt a {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
}

.upgradePrompt a:hover {
    text-decoration: underline;
    color: #0056b3;
}

/* Container for AdBanner on HomePage */
.adContainer {
  margin-top: 2.5rem; /* More space above ads */
  margin-bottom: 1.5rem; /* Space below ads */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  /* The AdBanner component itself might have min-height or specific dimensions */
}
