# Netlify Redirects for Ghost<PERSON><PERSON>er AI Text Humanizer
# https://docs.netlify.com/routing/redirects/

# API Routes to Netlify Functions
/api/process-text /.netlify/functions/process-text 200
/api/auth/callback/google /.netlify/functions/auth-callback-google 200
/api/stripe/webhook /.netlify/functions/stripe-webhook 200
/api/user/profile /.netlify/functions/user-profile 200
/api/stripe/create-checkout-session /.netlify/functions/create-checkout-session 200
/api/health /.netlify/functions/health 200

# SEO-friendly redirects for old URLs
/home / 301
/humanize / 301
/ai-text-humanizer / 301
/bypass-ai-detection / 301
/make-ai-undetectable / 301

# Redirect common misspellings and variations
/ghost-layer / 301
/ghostlayer-ai / 301
/ai-humanizer / 301
/text-humanizer / 301
/chatgpt-humanizer / 301
/gpt-humanizer / 301

# Redirect competitor comparison pages
/vs-quillbot /features/ 301
/vs-paraphraser /features/ 301
/vs-spinbot /features/ 301
/quillbot-alternative /features/ 301
/paraphraser-alternative /features/ 301

# Redirect feature-specific pages
/bypass-gptzero /#gptzero-bypass 301
/bypass-turnitin /#turnitin-bypass 301
/bypass-originality /#originality-bypass 301
/bypass-winston /#winston-bypass 301

# Redirect use case pages
/academic-writing /#academic-use 301
/content-creation /#content-creation 301
/business-writing /#business-writing 301
/creative-writing /#creative-writing 301

# Redirect help and support pages
/help /about/ 301
/support /about/ 301
/contact /about/ 301
/faq /#faq 301

# Redirect pricing variations
/plans /pricing/ 301
/subscription /pricing/ 301
/upgrade /pricing/ 301
/premium /pricing/ 301

# Redirect blog and resources (if added later)
/blog /about/ 301
/resources /features/ 301
/guides /features/ 301
/tutorials /#how-to-humanize 301

# Redirect social media and external links
/twitter https://twitter.com/ghostlayer_ai 301
/github https://github.com/HectorTa1989/stealthwriter-ai 301
/discord https://discord.gg/ghostlayer 301

# Redirect old domain variations (if applicable)
/stealthwriter / 301
/stealth-writer / 301

# Handle trailing slashes consistently
/features /features/ 301
/about /about/ 301
/pricing /pricing/ 301

# Redirect common search terms to relevant pages
/how-to-humanize-ai-text /#how-to-humanize 301
/how-to-bypass-ai-detection /#bypass-detection 301
/make-chatgpt-undetectable /#undetectable-ai 301
/free-ai-humanizer / 301

# Redirect tool-specific searches
/gptzero-bypass /#gptzero-bypass 301
/turnitin-ai-detection /#turnitin-bypass 301
/originality-ai-bypass /#originality-bypass 301
/winston-ai-bypass /#winston-bypass 301

# Redirect comparison searches
/best-ai-humanizer /features/ 301
/ai-humanizer-comparison /features/ 301
/quillbot-vs-ghostlayer /features/ 301

# Handle mobile and app redirects
/mobile / 301
/app / 301
/download / 301

# Redirect API documentation (if added later)
/api-docs /about/ 301
/developers /about/ 301

# Handle old file extensions
/index.html / 301
/features.html /features/ 301
/about.html /about/ 301
/pricing.html /pricing/ 301

# Redirect common typos in domain
/gostlayer / 301
/ghostlayar / 301
/ghost-layar / 301

# Handle www subdomain (if not using www)
https://www.ghostlayer.netlify.app/* https://ghostlayer.netlify.app/:splat 301!

# Handle different protocols
http://ghostlayer.netlify.app/* https://ghostlayer.netlify.app/:splat 301!

# Catch-all for SPA routing - must be last
/* /index.html 200
