#!/bin/bash

# <PERSON>ript to stop both Python microservices for the StealthWriter AI application

echo "Stopping StealthWriter AI Python Services..."

# Function to stop a service using its PID file
stop_service() {
    local service_name=$1
    local pid_file="${service_name,,}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        echo "Stopping $service_name (PID: $pid)..."
        
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            sleep 2
            
            # Check if process is still running
            if kill -0 $pid 2>/dev/null; then
                echo "Process $pid didn't stop gracefully, forcing termination..."
                kill -9 $pid
            fi
            
            echo "✓ $service_name stopped successfully"
        else
            echo "Process $pid is not running"
        fi
        
        rm "$pid_file"
    else
        echo "PID file for $service_name not found. Service may not be running or was started manually."
    fi
}

# Get the script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Stop PEGASUS service
cd "$PROJECT_ROOT/python_services/paraphrase_pegasus" 2>/dev/null
stop_service "PEGASUS"

# Stop NLTK Humanizer service
cd "$PROJECT_ROOT/python_services/nltk_humanizer" 2>/dev/null
stop_service "NLTK-Humanizer"

echo ""
echo "=== All Services Stopped ==="

# Also kill any remaining Python processes on the expected ports (cleanup)
echo "Cleaning up any remaining processes on ports 5001 and 5002..."

# Kill processes on port 5001 (PEGASUS)
lsof -ti:5001 | xargs kill -9 2>/dev/null && echo "Cleaned up port 5001" || echo "Port 5001 was already free"

# Kill processes on port 5002 (NLTK)
lsof -ti:5002 | xargs kill -9 2>/dev/null && echo "Cleaned up port 5002" || echo "Port 5002 was already free"

echo "Cleanup complete."
