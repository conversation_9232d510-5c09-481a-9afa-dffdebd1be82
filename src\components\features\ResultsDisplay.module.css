.resultsDisplay {
    margin-top: 30px; /* Increased margin */
    padding: 20px 25px; /* More padding */
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08); /* Softer, more pronounced shadow */
}

.resultsDisplay h4 {
    margin-top: 0;
    margin-bottom: 18px; /* More space below heading */
    color: #343a40; /* Darker heading color */
    font-size: 1.2rem; /* Slightly larger heading */
    border-bottom: 1px solid #eee; /* Separator for heading */
    padding-bottom: 10px;
}

.resultsDisplay p {
    margin-bottom: 10px; /* More space between paragraphs */
    font-size: 0.98rem; /* Slightly larger paragraph text */
    line-height: 1.6;
    color: #555;
}

.resultsDisplay p strong {
    color: #333;
    margin-right: 5px; /* Space after bolded label */
}

/* Specific styling for error messages within results display */
.errorMessage {
    color: #d9534f; /* Bootstrap danger color */
    font-weight: bold;
}

.statusHuman {
    color: #28a745; /* Green for human */
    font-weight: bold;
}

.statusAI {
    color: #dc3545; /* Red for AI */
    font-weight: bold;
}

.statusMixed {
    color: #ffc107; /* Yellow for mixed */
    font-weight: bold;
}

.scoreDetails {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px dashed #eee;
}
