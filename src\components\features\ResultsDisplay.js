import React from 'react';
import styles from './ResultsDisplay.module.css'; // Import the CSS module

const ResultsDisplay = ({ result }) => {
    if (!result) return null;

    // Determine status class for conditional styling
    let statusClass = '';
    if (result.status) {
        const lowerStatus = result.status.toLowerCase();
        if (lowerStatus.includes('human')) {
            statusClass = styles.statusHuman;
        } else if (lowerStatus.includes('ai') || lowerStatus.includes('potentially')) {
            statusClass = styles.statusAI;
        } else if (lowerStatus.includes('mixed')) {
            statusClass = styles.statusMixed;
        }
    }

    if (result.error && result.message) {
         return (
            <div className={styles.resultsDisplay}>
                <h4>AI Detection Check:</h4>
                <p className={styles.errorMessage}><strong>Error:</strong> {result.message}</p>
                {result.status && <p><strong>Status:</strong> <span className={statusClass || ''}>{result.status}</span></p>}
            </div>
        );
    }

    return (
        <div className={styles.resultsDisplay}>
            <h4>AI Detection Check:</h4>
            <p>
                <strong>Status:</strong>
                <span className={statusClass || ''}> {result.status || 'N/A'}</span>
            </p>
            <p>
                <strong>AI Likelihood Score:</strong>
                {result.score !== undefined && result.score !== null ? `${(result.score * 100).toFixed(1)}%` : 'N/A'}
            </p>
            {result.average_generated_prob !== undefined && (
                 <p>
                    <strong>Average AI Probability (Sentences):</strong>
                    {` ${(result.average_generated_prob * 100).toFixed(1)}%`}
                </p>
            )}
            {result.details && <p className={styles.scoreDetails}><strong>Details:</strong> {result.details}</p>}

            {/* Example of displaying sentence-level analysis if available */}
            {/* {result.sentences && result.sentences.length > 0 && (
                <div className={styles.scoreDetails}>
                    <strong>Sentence Analysis:</strong>
                    <ul>
                        {result.sentences.slice(0, 3).map((s, index) => ( // Show first 3 sentences
                            <li key={index}>
                                "{s.sentence.substring(0, 50)}..." - AI Prob: {(s.ai_prob * 100).toFixed(0)}%
                            </li>
                        ))}
                    </ul>
                </div>
            )} */}
        </div>
    );
};

export default ResultsDisplay;
